import os
import re
from aipyapp import runtime

def analyze_jsp_file(filepath, rel_path):
    """分析单个 JSP 文件的安全问题"""
    issues = []
    metrics = {
        'lines': 0,
        'scriptlets': 0,
        'expressions': 0,
        'includes': 0,
        'forms': 0
    }
    
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            lines = content.split('\n')
            metrics['lines'] = len(lines)
            
        # 检测 JSP 特有的安全问题
        jsp_patterns = {
            'scriptlet_code': r'<%.*?%>',  # JSP 脚本片段
            'expression': r'<%=.*?%>',   # JSP 表达式
            'include_directive': r'<%@.*?include.*?%>',  # 包含指令
            'form_action': r'<form[^>]*action=["\'][^"\']*["\'][^>]*>',  # 表单
            'sql_in_jsp': r'(SELECT|INSERT|UPDATE|DELETE).*\+.*\+',  # SQL 拼接
            'hardcoded_db': r'(jdbc:mysql|oracle:thin|sqlserver)',  # 硬编码数据库连接
            'session_direct': r'session\.(getAttribute|setAttribute|invalidate)',  # 会话操作
            'request_params': r'request\.(getParameter|getParameterValues)',  # 请求参数
            'out_print': r'out\.print|out\.println',  # 直接输出
            'file_upload': r'enctype="multipart/form-data"',  # 文件上传
            'error_page': r'<%@.*?errorPage.*?%>',  # 错误页面
            'xss_vulnerable': r'<%=.*?request\.getParameter.*?%>',  # XSS 风险
            'path_traversal': r'\.\.\/|\.\.\\',  # 路径遍历
            'commented_code': r'<%--.*?--%>',  # JSP 注释
        }
        
        for issue_type, pattern in jsp_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                issues.append({
                    'type': issue_type,
                    'count': len(matches),
                    'sample': matches[0][:100] + '...' if len(matches[0]) > 100 else matches[0]
                })
                
                # 更新指标
                if issue_type == 'scriptlet_code':
                    metrics['scriptlets'] = len(matches)
                elif issue_type == 'expression':
                    metrics['expressions'] = len(matches)
                elif issue_type == 'include_directive':
                    metrics['includes'] = len(matches)
                elif issue_type == 'form_action':
                    metrics['forms'] = len(matches)
                    
        # 检查敏感信息泄露
        sensitive_patterns = {
            'password_in_code': r'password\s*=\s*["\'][^"\']{5,}["\']',
            'api_key_in_code': r'(api[_-]?key|secret|token)\s*=\s*["\'][^"\']{10,}["\']',
            'db_connection': r'Connection\s+conn\s*=',
            'hardcoded_ip': r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b',
        }
        
        for issue_type, pattern in sensitive_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                issues.append({
                    'type': issue_type,
                    'count': len(matches),
                    'sample': matches[0][:50] + '...' if len(matches[0]) > 50 else matches[0]
                })
                
    except Exception as e:
        issues.append({'type': 'read_error', 'message': str(e)})
    
    return {'issues': issues, 'metrics': metrics, 'rel_path': rel_path}

# 获取 JSP 文件列表
jsp_state = runtime.get_block_state("scan_jsp_files")
if not jsp_state or not jsp_state.get("success"):
    print("❌ JSP 文件扫描未完成", file=sys.stderr)
    runtime.set_state(False, error="JSP 文件扫描未完成")
    exit(1)

jsp_files = jsp_state["jsp_files"]
total_jsp = len(jsp_files)

print(f"🔍 开始分析 {total_jsp} 个 JSP 文件的安全问题...")

# 分析结果汇总
all_results = []
summary_stats = {
    'total_lines': 0,
    'total_scriptlets': 0,
    'total_expressions': 0,
    'files_with_issues': 0,
    'issue_types': {},
    'high_risk_files': []
}

# 逐个分析 JSP 文件
for i, file_info in enumerate(jsp_files):
    result = analyze_jsp_file(file_info['path'], file_info['rel_path'])
    all_results.append(result)
    
    # 更新汇总统计
    metrics = result['metrics']
    summary_stats['total_lines'] += metrics['lines']
    summary_stats['total_scriptlets'] += metrics['scriptlets']
    summary_stats['total_expressions'] += metrics['expressions']
    
    # 统计问题
    if result['issues']:
        summary_stats['files_with_issues'] += 1
        for issue in result['issues']:
            issue_type = issue['type']
            if issue_type not in summary_stats['issue_types']:
                summary_stats['issue_types'][issue_type] = 0
            summary_stats['issue_types'][issue_type] += issue.get('count', 1)
        
        # 标记高风险文件
        high_risk_issues = ['sql_in_jsp', 'xss_vulnerable', 'hardcoded_db', 'password_in_code']
        if any(issue['type'] in high_risk_issues for issue in result['issues']):
            summary_stats['high_risk_files'].append(file_info['rel_path'])
    
    # 进度显示
    if (i + 1) % 20 == 0 or i == total_jsp - 1:
        print(f"📊 已分析 {i + 1}/{total_jsp} 个 JSP 文件...")

# 保存分析结果
runtime.set_state(True, 
                  jsp_analysis_results=all_results,
                  jsp_summary_stats=summary_stats)

print(f"✅ JSP 安全分析完成！")
print(f"📊 共分析 {total_jsp} 个 JSP 文件")
print(f"⚠️  发现 {summary_stats['files_with_issues']} 个文件存在问题")
print(f"🔴 高风险文件: {len(summary_stats['high_risk_files'])} 个")