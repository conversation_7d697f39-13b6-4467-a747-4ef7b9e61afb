import os
import sys
from aipyapp import runtime

# 获取上一步状态
state = runtime.get_block_state("locate_folder")
if not state or not state.get("success"):
    print("❌ 上一步未成功执行，请先定位目标文件夹", file=sys.stderr)
    runtime.set_state(False, error="前置任务失败")
    exit(1)

target_folder = state["target_path"]

# 常见代码文件扩展名
code_extensions = {
    '.py', '.js', '.ts', '.java', '.cpp', '.c', '.cs', '.go', '.rb', '.php',
    '.html', '.css', '.scss', '.json', '.xml', '.sh', '.bat', '.sql'
}

code_files = []
for root, dirs, files in os.walk(target_folder):
    for file in files:
        ext = os.path.splitext(file)[1].lower()
        if ext in code_extensions:
            full_path = os.path.join(root, file)
            rel_path = os.path.relpath(full_path, target_folder)
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
            except Exception as e:
                print(f"⚠️ 无法读取文件 {rel_path}: {str(e)}", file=sys.stderr)
                lines = 0
            code_files.append({
                "path": full_path,
                "rel_path": rel_path,
                "ext": ext,
                "lines": lines
            })

# 按行数降序排序
code_files.sort(key=lambda x: x["lines"], reverse=True)

runtime.set_state(True, code_files=code_files, summary={
    "total_files": len(code_files),
    "by_extension": {ext: len([f for f in code_files if f["ext"] == ext]) for ext in set(f["ext"] for f in code_files)}
})

print(f"✅ 已扫描完成，共找到 {len(code_files)} 个代码文件：")
for file in code_files:
    print(f"  📄 {file['rel_path']} ({file['lines']} 行)")