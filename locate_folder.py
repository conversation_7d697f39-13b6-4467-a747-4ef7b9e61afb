import os
import platform
import sys
from aipyapp import runtime

def get_desktop_path():
    system = platform.system()
    if system == "Windows":
        return os.path.join(os.environ['USERPROFILE'], 'Desktop')
    else:
        return os.path.join(os.path.expanduser('~'), 'Desktop')

desktop = get_desktop_path()
target_folder = os.path.join(desktop, '2503')

if os.path.exists(target_folder) and os.path.isdir(target_folder):
    file_count = len([f for f in os.listdir(target_folder) if os.path.isfile(os.path.join(target_folder, f))])
    runtime.set_state(True, target_path=target_folder, file_count=file_count)
    print(f"✅ 目标文件夹已找到：{target_folder}")
    print(f"📁 包含 {file_count} 个文件")
else:
    runtime.set_state(False, error=f"❌ 文件夹未找到：{target_folder}")
    print(f"❌ 指定的文件夹不存在或不是目录：{target_folder}", file=sys.stderr)