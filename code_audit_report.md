# 代码审计报告

## 项目概览
- **总文件数**: 1,562
- **总代码行数**: 132,881
- **注释行数**: 11,636
- **注释率**: 8.8%
- **主要技术栈**: JavaScript, HTML, CSS, JSON, SQL

## 文件分布
- `.js`: 77 个文件 (103,025 行)
- `.html`: 3 个文件 (11,099 行)
- `.json`: 3 个文件 (5,993 行)
- `.css`: 17 个文件 (12,764 行)

## 安全问题统计
- **eval**: 4 处
- **exec**: 8 处
- **xss_vulnerable**: 91 处
- **sql_injection**: 40 处

## 高风险文件清单
### mdms\functionModule\pubNew\js\ueditor.all.js
- eval: 3 处 (eval()
- exec: 8 处 (exec()
- xss_vulnerable: 73 处 (innerHTML =)
- sql_injection: 5 处 ("("+r.responseText+")

### frame\eventStatistics\fishboneDiagram\src\js\html2canvas2.js
- xss_vulnerable: 1 处 (innerHTML =)

### mdms\functionModule\newDoctorFile\js\newDoctorInfo.js
- sql_injection: 1 处 ("("+groupList[i].IdentityName+")

### _customize\mdms\functionModule\medoctorVirtueManage\js\doctorAssessmentEdit.js
- xss_vulnerable: 1 处 (innerHTML =)

### mdms\functionModule\medoctorVirtueManage\js\doctorAssessmentEdit.js
- xss_vulnerable: 1 处 (innerHTML =)
- sql_injection: 2 处 ("select[name="+currName+")

### frame\js\common.js
- xss_vulnerable: 9 处 (document.write)
- sql_injection: 2 处 ("dialogWidth:"+ popwidth +"px;dialogHeight:"+ poph...)

### mdms\mobile\plugins\assemblys\mdmsAssemblys.js
- sql_injection: 1 处 ("年" + month + "月" + day + "日 " + hours + "时" + min...)

### mdms\functionModule\doctorVirtueManage\js\doctorAssessmentEdit.js
- xss_vulnerable: 1 处 (innerHTML =)
- sql_injection: 2 处 ("select[name="+currName+")

### frame\css\default\common.css
- eval: 1 处 (eval()

### mdms\functionModule\newTechniqueManager\js\doctorInfoDetail.js
- sql_injection: 1 处 ("#detailFrame").attr('src',"../../../frame/customF...)

### mdms\functionModule\newDoctorFile\js\doctorNegative.js
- xss_vulnerable: 1 处 (innerHTML =)

- xss_vulnerable: 1 处 (innerHTML =)

### mdms\functionModule\mdmsCustomDetail\js\initCustomDetail.js
- xss_vulnerable: 2 处 (innerHTML =)

### mdms\functionModule\userDeptOperation\js\userDeptOperationList.js
- sql_injection: 4 处 ("mdms/deptOperation/getUserDeptOperationPager.spri...)

### _customize\mdms\functionModule\userDeptLimitOperation\js\userDeptLimitOperationList.js
- sql_injection: 4 处 ("mdms/deptOperation/getUserDeptOperationPager.spri...)

### _customize\mdms\functionModule\userDeptOperation\js\userDeptOperationList.js
- sql_injection: 4 处 ("mdms/deptOperation/getUserDeptOperationPager.spri...)

### mdms\functionModule\newTechniqueManager\js\tool.js
- sql_injection: 13 处 ("innerHTML" : "<i class='layui-icon layui-icon-edi...)

### mdms\functionModule\workloadStatistics\js\workloadStatistics.js
- xss_vulnerable: 1 处 (innerHTML =)

### mdms\functionModule\workloadStatistics\js\workloadStatisticsList.js
- sql_injection: 1 处 ("<option value="+comp.compNo+">"+comp.compName+")

