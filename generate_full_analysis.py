from aipyapp import runtime
import json

# 获取之前的状态
scan_state = runtime.get_block_state("scan_code_files")
quality_state = runtime.get_block_state("analyze_code_quality")

if not scan_state or not scan_state.get("success"):
    print("❌ 文件扫描未完成", file=sys.stderr)
    runtime.set_state(False, error="文件扫描未完成")
    exit(1)

if not quality_state or not quality_state.get("success"):
    print("❌ 代码质量分析未完成", file=sys.stderr)
    runtime.set_state(False, error="代码质量分析未完成")
    exit(1)

code_files = scan_state["code_files"]
analysis_results = quality_state["analysis_results"]
summary_stats = quality_state["summary_stats"]

# 生成详细的审计报告
report = f"""# 代码审计报告

## 项目概览
- **总文件数**: {len(code_files):,}
- **总代码行数**: {summary_stats['total_lines']:,}
- **注释行数**: {summary_stats['total_comment_lines']:,}
- **注释率**: {summary_stats['total_comment_lines']/summary_stats['total_lines']*100:.1f}%
- **主要技术栈**: JavaScript, HTML, CSS, JSON, SQL

## 文件分布
"""
for ext, stats in summary_stats['extension_stats'].items():
    report += f"- `{ext}`: {stats['files']} 个文件 ({stats['lines']:,} 行)\n"

report += f"""
## 安全问题统计
"""
if summary_stats['issue_types']:
    for issue_type, count in summary_stats['issue_types'].items():
        report += f"- **{issue_type}**: {count} 处\n"
else:
    report += "- 未发现明显安全问题\n"

report += """
## 高风险文件清单
"""

high_risk_files = []
for result in analysis_results:
    if result['issues']:
        high_risk_files.append(result)

for file in high_risk_files:
    report += f"### {file['rel_path']}\n"
    for issue in file['issues']:
        report += f"- {issue['type']}: {issue.get('count', 1)} 处 ({issue['sample']})\n"
    report += "\n"

# 保存报告
report_path = "code_audit_report.md"
with open(report_path, "w", encoding="utf-8") as f:
    f.write(report)

runtime.set_state(True, 
                  report_path=report_path,
                  report_content=report,
                  high_risk_count=len(high_risk_files))

print(f"✅ 审计报告已生成！")
print(f"📄 报告路径: {report_path}")
print(f"⚠️  高风险文件: {len(high_risk_files)} 个")