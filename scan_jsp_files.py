import os
from aipyapp import runtime

# 获取上一步状态
state = runtime.get_block_state("locate_folder")
if not state or not state.get("success"):
    print("❌ 上一步未成功执行，请先定位目标文件夹", file=sys.stderr)
    runtime.set_state(False, error="前置任务失败")
    exit(1)

target_folder = state["target_path"]

# 查找所有 .jsp 文件
jsp_files = []
for root, dirs, files in os.walk(target_folder):
    for file in files:
        if file.lower().endswith('.jsp'):
            full_path = os.path.join(root, file)
            rel_path = os.path.relpath(full_path, target_folder)
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    lines = len(f.readlines())
            except Exception as e:
                print(f"⚠️ 无法读取文件 {rel_path}: {str(e)}", file=sys.stderr)
                lines = 0
            jsp_files.append({
                "path": full_path,
                "rel_path": rel_path,
                "lines": lines
            })

# 按行数降序排序
jsp_files.sort(key=lambda x: x["lines"], reverse=True)

runtime.set_state(True, jsp_files=jsp_files, summary={"total": len(jsp_files)})

if jsp_files:
    print(f"✅ 找到 {len(jsp_files)} 个 JSP 文件：")
    for file in jsp_files:
        print(f"  📄 {file['rel_path']} ({file['lines']} 行)")
else:
    print("🔍 未找到任何 JSP 文件")