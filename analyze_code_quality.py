import os
import re
from aipyapp import runtime
import json

def analyze_file(filepath, rel_path, ext):
    """分析单个文件的质量和安全问题"""
    issues = []
    metrics = {
        'lines': 0,
        'comment_lines': 0,
        'code_lines': 0,
        'functions': 0,
        'classes': 0
    }
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
            metrics['lines'] = len(lines)
            
        # 计算注释行数（简单统计）
        comment_pattern = r'(//.*?$|/\*.*?\*/|#.*?$|<!--.*?-->)'
        comments = re.findall(comment_pattern, content, re.MULTILINE | re.DOTALL)
        metrics['comment_lines'] = len(comments)
        metrics['code_lines'] = metrics['lines'] - metrics['comment_lines']
        
        # 检测敏感函数
        sensitive_patterns = {
            'eval': r'\beval\s*\(',
            'exec': r'\bexec\s*\(',
            'pickle_load': r'pickle\.loads?\(',
            'os_system': r'os\.system\s*\(',
            'subprocess': r'subprocess\.call|subprocess\.run|subprocess\.Popen',
            'hardcoded_password': r'password\s*=\s*[\'"][^\'"]{5,}[\'"]',
            'hardcoded_key': r'(api[_-]?key|secret|token)\s*=\s*[\'"][^\'"]{10,}[\'"]',
            'xss_vulnerable': r'document\.write|innerHTML\s*=',
            'sql_injection': r'".*\+.*\+"|f".*\{.*\}.*"'
        }
        
        for issue_type, pattern in sensitive_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                issues.append({
                    'type': issue_type,
                    'count': len(matches),
                    'sample': matches[0][:50] + '...' if len(matches[0]) > 50 else matches[0]
                })
                
        # 统计函数和类（JS/Python）
        if ext in ['.js', '.py']:
            metrics['functions'] = len(re.findall(r'\bfunction\s+\w+|def\s+\w+', content))
            metrics['classes'] = len(re.findall(r'\bclass\s+\w+', content))
            
    except Exception as e:
        issues.append({'type': 'read_error', 'message': str(e)})
    
    return {'issues': issues, 'metrics': metrics, 'rel_path': rel_path}

# 获取上一步状态
code_files_state = runtime.get_block_state("scan_code_files")
if not code_files_state or not code_files_state.get("success"):
    print("❌ 前置任务失败，请先扫描代码文件", file=sys.stderr)
    runtime.set_state(False, error="前置任务失败")
    exit(1)

code_files = code_files_state["code_files"]
total_files = len(code_files)

print(f"🔍 开始分析 {total_files} 个代码文件的质量和安全问题...")

# 分析结果汇总
all_results = []
summary_stats = {
    'total_lines': 0,
    'total_comment_lines': 0,
    'total_code_lines': 0,
    'files_with_issues': 0,
    'issue_types': {},
    'extension_stats': {}
}

# 逐个分析文件
for i, file_info in enumerate(code_files[:100]):  # 先分析前100个文件，避免超时
    result = analyze_file(file_info['path'], file_info['rel_path'], file_info['ext'])
    all_results.append(result)
    
    # 更新汇总统计
    metrics = result['metrics']
    summary_stats['total_lines'] += metrics['lines']
    summary_stats['total_comment_lines'] += metrics['comment_lines']
    summary_stats['total_code_lines'] += metrics['code_lines']
    
    ext = file_info['ext']
    if ext not in summary_stats['extension_stats']:
        summary_stats['extension_stats'][ext] = {'files': 0, 'lines': 0}
    summary_stats['extension_stats'][ext]['files'] += 1
    summary_stats['extension_stats'][ext]['lines'] += metrics['lines']
    
    # 统计问题
    if result['issues']:
        summary_stats['files_with_issues'] += 1
        for issue in result['issues']:
            issue_type = issue['type']
            if issue_type not in summary_stats['issue_types']:
                summary_stats['issue_types'][issue_type] = 0
            summary_stats['issue_types'][issue_type] += issue.get('count', 1)
    
    # 进度显示
    if (i + 1) % 20 == 0 or i == total_files - 1:
        print(f"📊 已分析 {i + 1}/{min(100, total_files)} 个文件...")

# 保存分析结果
runtime.set_state(True, 
                  analysis_results=all_results,
                  summary_stats=summary_stats,
                  total_analyzed=min(100, total_files))

print(f"✅ 代码质量分析完成！")
print(f"📊 共分析 {min(100, total_files)} 个文件")
print(f"⚠️  发现 {summary_stats['files_with_issues']} 个文件存在问题")
print(f"📝 总代码行数: {summary_stats['total_lines']:,}")
print(f"💬 注释行数: {summary_stats['total_comment_lines']:,}")